export enum CollectionStatus {
  PREMARKET = 'PREMARKET',
  MARKET = 'MARKET',
  DELETED = 'DELETED',
}

export interface CollectionEntity {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: Date;
  floorPrice: number; // Minimum floor price for collection items in TON
}

export const COLLECTION_STATUS_TEXT = {
  [CollectionStatus.PREMARKET]: 'Pre-market',
  [CollectionStatus.MARKET]: 'Market',
  [CollectionStatus.DELETED]: 'Deleted',
};

export enum Role {
  ADMIN = 'admin',
  USER = 'user',
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  name?: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role?: 'admin' | 'user';
  tg_id?: string;
  ton_wallet_address?: string;
  referrer_id?: string; // Telegram ID of the user who referred this user
  referral_fee?: number; // Custom referral fee in BPS (basis points)
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = 'active',
  PAID = 'paid',
  GIFT_SENT_TO_RELAYER = 'gift_sent_to_relayer',
  FULFILLED = 'fulfilled',
  CANCELLED = 'cancelled',
}

export interface OrderEntity {
  id?: string;
  number?: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId?: string; // Optional since orders can be created without a seller
  collectionId: string; // Collection ID for floor price validation
  price: number;
  status: OrderStatus;
  deadline?: Date; // Deadline for seller to fulfill the order
  giftSentToRelayerAt?: Date; // When gift was sent to relayer
  owned_gift_id?: string; // ID of the gift owned by the user creating the order
  secondaryMarketPrice?: number | null; // Price set by buyer for reselling on secondary market
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppConfigEntity {
  id: string;
  purchase_fee: number; // in BPS
  cancel_order_fee: number; // in BPS - dynamic fee for two-person order cancellations
  referrer_fee: number; // in BPS
  min_deposit_amount: number; // in TON
  min_withdrawal_amount: number; // in TON
  max_withdrawal_amount: number; // in TON
  buyer_lock_percentage: number; // Percentage of order amount locked by buyer (0.0-1.0)
  seller_lock_percentage: number; // Percentage of order amount locked by seller (0.0-1.0)
  deposit_fee: number; // in TON
  withdrawal_fee: number; // in TON
  min_secondary_market_price: number; // in TON
  fixed_cancel_order_fee: number; // Static TON value for single-person order cancellations
}

export interface TxLookupEntity {
  last_checked_record_id: string;
}

export const APP_CONFIG_COLLECTION = 'app_config';
export const APP_CONFIG_DOC_ID = 'main';
export const APP_USERS_COLLECTION = 'users';
export const CACHE_CONFIG = { duration: 24 * 60 * 60 * 1000 }; // 1 day

export const MARKETPLACE_REVENUE_USER_ID = 'marketplace_revenue';

export enum UserType {
  BUYER = 'buyer',
  SELLER = 'seller',
}

export enum CachePatterns {
  ORDERS_FOR_BUYERS = 'getOrdersForBuyers',
  ORDERS_FOR_SELLERS = 'getOrdersForSellers',
  SECONDARY_MARKET_ORDERS = 'getSecondaryMarketOrders',
  USER_ORDERS = 'getUserOrders',
  COLLECTIONS = 'collections',
  APP_CONFIG = 'app_config',
}

export enum AppCloudFunctions {
  withdrawRevenue = 'withdrawRevenue',
  setSecondaryMarketPrice = 'setSecondaryMarketPrice',
  makeSecondaryMarketPurchase = 'makeSecondaryMarketPurchase',
  cancelUserOrder = 'cancelUserOrder',
  makePurchaseAsSeller = 'makePurchaseAsSeller',
  makePurchaseAsBuyer = 'makePurchaseAsBuyer',
  changeUserData = 'changeUserData',
  signInWithTelegram = 'signInWithTelegram',
}

export const STARS_PER_GIFT_SEND = 25;

export const COLLECTION_NAME = 'collections';
export const COLLECTION_CACHE_CONFIG = { duration: 24 * 60 * 60 * 1000 }; // 1 day

export const ORDERS_COLLECTION_NAME = 'orders';

export enum AppRoutes {
  MARKETPLACE = '/',
  SECONDARY_MARKET = '/secondary-market',
  ORDERS = '/orders',
  PROFILE = '/profile',
  ADMIN = '/admin',
  AUTH = '/auth',
}

export const TELEGRAM_BOT_URL = `https://t.me/${process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME}`;

export const FREEZE_PERIOD_DAYS = 21;
export const FREEZE_PERIOD_MS = FREEZE_PERIOD_DAYS * 24 * 60 * 60 * 1000;
